import { 
  users, workspaces, boards, items, activities, comments,
  type User, type Workspace, type Board, type Item, type Activity, type Comment,
  type InsertUser, type InsertWorkspace, type InsertBoard, type InsertItem, type InsertActivity, type InsertComment
} from "@shared/schema";

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByMondayId(mondayUserId: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserPresence(mondayUserId: string, isOnline: boolean): Promise<void>;
  getOnlineUsers(): Promise<User[]>;

  // Workspaces
  getWorkspaces(): Promise<Workspace[]>;
  getWorkspace(id: number): Promise<Workspace | undefined>;
  createWorkspace(workspace: InsertWorkspace): Promise<Workspace>;

  // Boards
  getBoardsByWorkspace(workspaceId: number): Promise<Board[]>;
  createBoard(board: InsertBoard): Promise<Board>;

  // Items
  getItemsByBoard(boardId: number): Promise<Item[]>;
  createItem(item: InsertItem): Promise<Item>;
  updateItem(mondayItemId: string, updates: Partial<InsertItem>): Promise<Item | undefined>;

  // Activities
  getActivities(filters?: { workspaceId?: number; userId?: number; limit?: number }): Promise<Activity[]>;
  createActivity(activity: InsertActivity): Promise<Activity>;
  getActivitiesWithDetails(filters?: { workspaceId?: number; userId?: number; limit?: number }): Promise<any[]>;

  // Comments
  getCommentsByItem(itemId: number): Promise<Comment[]>;
  createComment(comment: InsertComment): Promise<Comment>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User> = new Map();
  private workspaces: Map<number, Workspace> = new Map();
  private boards: Map<number, Board> = new Map();
  private items: Map<number, Item> = new Map();
  private activities: Map<number, Activity> = new Map();
  private comments: Map<number, Comment> = new Map();

  private currentUserId = 1;
  private currentWorkspaceId = 1;
  private currentBoardId = 1;
  private currentItemId = 1;
  private currentActivityId = 1;
  private currentCommentId = 1;

  constructor() {
    // Initialize with some sample data
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Create sample users
    const sampleUsers: User[] = [
      { id: 1, mondayUserId: "user_1", name: "Sarah Johnson", email: "<EMAIL>", avatar: null, isOnline: true, lastSeen: new Date() },
      { id: 2, mondayUserId: "user_2", name: "Mike Chen", email: "<EMAIL>", avatar: null, isOnline: true, lastSeen: new Date() },
      { id: 3, mondayUserId: "user_3", name: "Emma Rodriguez", email: "<EMAIL>", avatar: null, isOnline: true, lastSeen: new Date() },
      { id: 4, mondayUserId: "user_4", name: "John Doe", email: "<EMAIL>", avatar: null, isOnline: false, lastSeen: new Date(Date.now() - 30 * 60 * 1000) },
      { id: 5, mondayUserId: "user_5", name: "Anna Liu", email: "<EMAIL>", avatar: null, isOnline: false, lastSeen: new Date(Date.now() - 60 * 60 * 1000) },
    ];

    // Create sample workspaces
    const sampleWorkspaces: Workspace[] = [
      { id: 1, mondayWorkspaceId: "ws_1", name: "Marketing Team", description: "Marketing campaigns and content", isActive: true },
      { id: 2, mondayWorkspaceId: "ws_2", name: "Product Development", description: "Product roadmap and development", isActive: true },
      { id: 3, mondayWorkspaceId: "ws_3", name: "Sales Operations", description: "Sales pipeline and operations", isActive: true },
    ];

    // Create sample boards
    const sampleBoards: Board[] = [
      { id: 1, mondayBoardId: "board_1", workspaceId: 1, name: "Marketing Campaigns", description: "Campaign planning and execution" },
      { id: 2, mondayBoardId: "board_2", workspaceId: 2, name: "Product Roadmap", description: "Feature development roadmap" },
      { id: 3, mondayBoardId: "board_3", workspaceId: 3, name: "Sales Pipeline", description: "Sales opportunities and leads" },
    ];

    // Create sample items
    const sampleItems: Item[] = [
      { id: 1, mondayItemId: "item_1", boardId: 1, name: "Campaign Launch Checklist", status: "Done", assignedUserId: 1 },
      { id: 2, mondayItemId: "item_2", boardId: 2, name: "Product Roadmap Q1", status: "In Progress", assignedUserId: 2 },
      { id: 3, mondayItemId: "item_3", boardId: 3, name: "Client Presentation", status: "To Do", assignedUserId: 3 },
      { id: 4, mondayItemId: "item_4", boardId: 1, name: "Weekly Team Sync", status: "New", assignedUserId: 4 },
      { id: 5, mondayItemId: "item_5", boardId: 2, name: "Bug Fix #234", status: "In Progress", assignedUserId: 5 },
    ];

    // Create sample activities
    const sampleActivities: Activity[] = [
      { id: 1, userId: 1, workspaceId: 1, boardId: 1, itemId: 1, actionType: "update", description: "updated Campaign Launch Checklist", metadata: { status: "Done" }, createdAt: new Date(Date.now() - 2 * 60 * 1000) },
      { id: 2, userId: 2, workspaceId: 2, boardId: 2, itemId: 2, actionType: "comment", description: "commented on Product Roadmap Q1", metadata: { comment: "Great progress on the API integration. Should we schedule a review meeting?" }, createdAt: new Date(Date.now() - 5 * 60 * 1000) },
      { id: 3, userId: 3, workspaceId: 3, boardId: 3, itemId: 3, actionType: "assign", description: "assigned Client Presentation to John Doe", metadata: { assignedTo: "John Doe" }, createdAt: new Date(Date.now() - 12 * 60 * 1000) },
      { id: 4, userId: 4, workspaceId: 1, boardId: 1, itemId: 4, actionType: "create", description: "created Weekly Team Sync", metadata: { newItem: true }, createdAt: new Date(Date.now() - 18 * 60 * 1000) },
      { id: 5, userId: 5, workspaceId: 2, boardId: 2, itemId: 5, actionType: "move", description: "moved Bug Fix #234 to In Progress", metadata: { status: "In Progress" }, createdAt: new Date(Date.now() - 25 * 60 * 1000) },
    ];

    // Store sample data
    sampleUsers.forEach(user => this.users.set(user.id, user));
    sampleWorkspaces.forEach(workspace => this.workspaces.set(workspace.id, workspace));
    sampleBoards.forEach(board => this.boards.set(board.id, board));
    sampleItems.forEach(item => this.items.set(item.id, item));
    sampleActivities.forEach(activity => this.activities.set(activity.id, activity));

    // Set current IDs
    this.currentUserId = Math.max(...sampleUsers.map(u => u.id)) + 1;
    this.currentWorkspaceId = Math.max(...sampleWorkspaces.map(w => w.id)) + 1;
    this.currentBoardId = Math.max(...sampleBoards.map(b => b.id)) + 1;
    this.currentItemId = Math.max(...sampleItems.map(i => i.id)) + 1;
    this.currentActivityId = Math.max(...sampleActivities.map(a => a.id)) + 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByMondayId(mondayUserId: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.mondayUserId === mondayUserId);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async updateUserPresence(mondayUserId: string, isOnline: boolean): Promise<void> {
    const user = await this.getUserByMondayId(mondayUserId);
    if (user) {
      user.isOnline = isOnline;
      user.lastSeen = new Date();
      this.users.set(user.id, user);
    }
  }

  async getOnlineUsers(): Promise<User[]> {
    return Array.from(this.users.values()).filter(user => user.isOnline);
  }

  async getWorkspaces(): Promise<Workspace[]> {
    return Array.from(this.workspaces.values()).filter(ws => ws.isActive);
  }

  async getWorkspace(id: number): Promise<Workspace | undefined> {
    return this.workspaces.get(id);
  }

  async createWorkspace(insertWorkspace: InsertWorkspace): Promise<Workspace> {
    const id = this.currentWorkspaceId++;
    const workspace: Workspace = { ...insertWorkspace, id };
    this.workspaces.set(id, workspace);
    return workspace;
  }

  async getBoardsByWorkspace(workspaceId: number): Promise<Board[]> {
    return Array.from(this.boards.values()).filter(board => board.workspaceId === workspaceId);
  }

  async createBoard(insertBoard: InsertBoard): Promise<Board> {
    const id = this.currentBoardId++;
    const board: Board = { ...insertBoard, id };
    this.boards.set(id, board);
    return board;
  }

  async getItemsByBoard(boardId: number): Promise<Item[]> {
    return Array.from(this.items.values()).filter(item => item.boardId === boardId);
  }

  async createItem(insertItem: InsertItem): Promise<Item> {
    const id = this.currentItemId++;
    const item: Item = { ...insertItem, id };
    this.items.set(id, item);
    return item;
  }

  async updateItem(mondayItemId: string, updates: Partial<InsertItem>): Promise<Item | undefined> {
    const item = Array.from(this.items.values()).find(i => i.mondayItemId === mondayItemId);
    if (item) {
      Object.assign(item, updates);
      this.items.set(item.id, item);
      return item;
    }
    return undefined;
  }

  async getActivities(filters?: { workspaceId?: number; userId?: number; limit?: number }): Promise<Activity[]> {
    let activities = Array.from(this.activities.values());
    
    if (filters?.workspaceId) {
      activities = activities.filter(a => a.workspaceId === filters.workspaceId);
    }
    
    if (filters?.userId) {
      activities = activities.filter(a => a.userId === filters.userId);
    }
    
    // Sort by created date (newest first)
    activities.sort((a, b) => (b.createdAt?.getTime() || 0) - (a.createdAt?.getTime() || 0));
    
    if (filters?.limit) {
      activities = activities.slice(0, filters.limit);
    }
    
    return activities;
  }

  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const id = this.currentActivityId++;
    const activity: Activity = { ...insertActivity, id, createdAt: new Date() };
    this.activities.set(id, activity);
    return activity;
  }

  async getActivitiesWithDetails(filters?: { workspaceId?: number; userId?: number; limit?: number }): Promise<any[]> {
    const activities = await this.getActivities(filters);
    
    return activities.map(activity => {
      const user = this.users.get(activity.userId!);
      const workspace = this.workspaces.get(activity.workspaceId!);
      const board = this.boards.get(activity.boardId!);
      const item = this.items.get(activity.itemId!);
      
      return {
        ...activity,
        user: user ? { id: user.id, name: user.name, avatar: user.avatar } : null,
        workspace: workspace ? { id: workspace.id, name: workspace.name } : null,
        board: board ? { id: board.id, name: board.name } : null,
        item: item ? { id: item.id, name: item.name, status: item.status } : null,
      };
    });
  }

  async getCommentsByItem(itemId: number): Promise<Comment[]> {
    return Array.from(this.comments.values()).filter(comment => comment.itemId === itemId);
  }

  async createComment(insertComment: InsertComment): Promise<Comment> {
    const id = this.currentCommentId++;
    const comment: Comment = { ...insertComment, id, createdAt: new Date() };
    this.comments.set(id, comment);
    return comment;
  }
}

export const storage = new MemStorage();
