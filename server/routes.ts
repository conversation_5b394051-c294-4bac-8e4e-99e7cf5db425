import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import { insertActivitySchema } from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Get online users (presence)
  app.get("/api/users/online", async (req, res) => {
    try {
      const onlineUsers = await storage.getOnlineUsers();
      res.json(onlineUsers);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch online users" });
    }
  });

  // Get all workspaces
  app.get("/api/workspaces", async (req, res) => {
    try {
      const workspaces = await storage.getWorkspaces();
      res.json(workspaces);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workspaces" });
    }
  });

  // Get activities with optional filters
  app.get("/api/activities", async (req, res) => {
    try {
      const { workspaceId, userId, limit = 20 } = req.query;
      
      const filters = {
        workspaceId: workspaceId ? parseInt(workspaceId as string) : undefined,
        userId: userId ? parseInt(userId as string) : undefined,
        limit: parseInt(limit as string)
      };

      const activities = await storage.getActivitiesWithDetails(filters);
      res.json(activities);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch activities" });
    }
  });

  // Create new activity
  app.post("/api/activities", async (req, res) => {
    try {
      const validatedData = insertActivitySchema.parse(req.body);
      const activity = await storage.createActivity(validatedData);
      res.status(201).json(activity);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Invalid activity data", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create activity" });
      }
    }
  });

  // Get comments for an item
  app.get("/api/items/:itemId/comments", async (req, res) => {
    try {
      const itemId = parseInt(req.params.itemId);
      const comments = await storage.getCommentsByItem(itemId);
      res.json(comments);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch comments" });
    }
  });

  // Create new comment
  app.post("/api/items/:itemId/comments", async (req, res) => {
    try {
      const itemId = parseInt(req.params.itemId);
      const { content, userId } = req.body;
      
      const comment = await storage.createComment({
        mondayCommentId: `comment_${Date.now()}`,
        itemId,
        userId,
        content
      });
      
      res.status(201).json(comment);
    } catch (error) {
      res.status(500).json({ error: "Failed to create comment" });
    }
  });

  // Update user presence
  app.post("/api/users/:mondayUserId/presence", async (req, res) => {
    try {
      const { mondayUserId } = req.params;
      const { isOnline } = req.body;
      
      await storage.updateUserPresence(mondayUserId, isOnline);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to update user presence" });
    }
  });

  // Simulate Monday.com API endpoints for demo
  app.get("/api/monday/sync", async (req, res) => {
    try {
      // This would normally sync with Monday.com APIs
      // For demo purposes, we'll simulate some activity
      const activities = await storage.getActivitiesWithDetails({ limit: 10 });
      res.json({ 
        success: true, 
        synced: activities.length,
        message: "Successfully synced with Monday.com" 
      });
    } catch (error) {
      res.status(500).json({ error: "Failed to sync with Monday.com" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
