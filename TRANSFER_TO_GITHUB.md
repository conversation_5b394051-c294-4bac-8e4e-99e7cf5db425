# Transfer User Pulse to GitHub Repository

## Files to Transfer

### Essential Project Files
- `package.json` - Project dependencies and scripts
- `package-lock.json` - Dependency lock file
- `tsconfig.json` - TypeScript configuration
- `vite.config.ts` - Build configuration
- `tailwind.config.ts` - Styling configuration
- `postcss.config.js` - PostCSS configuration
- `components.json` - UI component configuration
- `drizzle.config.ts` - Database configuration
- `.gitignore` - Git ignore rules

### Source Code Directories
- `client/` - Frontend React application
  - `client/src/` - Source code
  - `client/index.html` - Entry point
- `server/` - Backend Express.js application
  - `server/index.ts` - Main server file
  - `server/routes.ts` - API routes
  - `server/storage.ts` - Data storage layer
  - `server/vite.ts` - Development server setup
- `shared/` - Shared TypeScript types and schemas
  - `shared/schema.ts` - Database schema and types

### Documentation Files
- `replit.md` - Project documentation and architecture
- `MONDAY_APP_SETUP.md` - Monday.com integration setup
- `monday-app-config.json` - Monday.com app configuration

### Files to Exclude
- `node_modules/` - Dependencies (will be installed via npm)
- `dist/` - Build output (will be generated)
- `.DS_Store` - macOS system files
- `server/public/` - Generated static files
- `attached_assets/` - Temporary files

## Transfer Instructions

### Option 1: Manual Transfer
1. Create a new repository on GitHub named `user-pulse`
2. Clone the repository to your local machine
3. Copy the essential files and directories listed above
4. Run `npm install` to install dependencies
5. Commit and push to GitHub

### Option 2: Download and Upload
1. Download the project files (excluding node_modules)
2. Create a new GitHub repository
3. Upload the files directly through GitHub's web interface
4. Use GitHub Codespaces or clone locally to run `npm install`

## Post-Transfer Setup

After moving to GitHub:

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Environment Variables:**
   - Set up `SESSION_SECRET` for production
   - Configure database connection if using PostgreSQL
   - Set up Monday.com API credentials if needed

3. **Development:**
   ```bash
   npm run dev
   ```

4. **Build for Production:**
   ```bash
   npm run build
   ```

## Key Features Preserved
- Real-time team presence tracking
- Activity feed with filtering
- Monday.com widget integration
- Responsive design with Tailwind CSS
- TypeScript throughout
- Express.js API with in-memory storage
- Drizzle ORM schema definitions

## Next Steps
- Set up GitHub Actions for CI/CD
- Configure deployment to your preferred hosting platform
- Set up environment variables for production
- Consider moving to a persistent database (PostgreSQL)