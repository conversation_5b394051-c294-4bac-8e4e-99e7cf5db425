# Monday.com App Setup Guide

## Key Considerations for Monday.com App Development

Based on the Monday.com developer documentation, here are the essential requirements and considerations for transforming User Pulse into a proper Monday.com marketplace app:

### 1. App Registration & Configuration

**Required Steps:**
- Register app in Monday.com Developer Portal
- Configure OAuth permissions and scopes
- Set up webhook endpoints for real-time updates
- Submit app for marketplace review

**App Configuration:**
```json
{
  "type": "dashboard-widget",
  "name": "User Pulse",
  "description": "Real-time team presence and activity visibility",
  "permissions": ["boards:read", "users:read", "workspaces:read"],
  "scopes": ["boards:read", "users:read"]
}
```

### 2. Technical Architecture Changes

**From Standalone to Monday Widget:**
- ✅ Added Monday SDK integration (`monday-sdk-js`)
- ✅ Created dual-mode app (standalone + Monday widget)
- ✅ Implemented Monday GraphQL API access
- ✅ Added widget context management
- ✅ Integrated Monday's authentication system

**Key Differences:**
- **Data Source**: Monday.com GraphQL API instead of custom backend
- **Authentication**: OAuth via Monday SDK instead of session management
- **Context**: Widget receives board IDs from dashboard context
- **Storage**: Monday's instance storage instead of PostgreSQL
- **UI**: Responsive design for dashboard widget constraints

### 3. Monday SDK Implementation

**Core SDK Features Used:**
```javascript
// Context management
monday.listen("context", (res) => {
  // Get connected board IDs and user context
});

// Settings management
monday.listen("settings", (res) => {
  // Get user-configured widget settings
});

// GraphQL API access
monday.api(`query { boards { ... } }`);

// UI interactions
monday.execute("openItemCard", { itemId: 123 });
```

### 4. Data Access & Permissions

**Required Permissions:**
- `boards:read` - Access board data and activities
- `users:read` - Get team member information
- `workspaces:read` - Access workspace context

**GraphQL Queries Used:**
```graphql
query GetBoardActivities($boardIds: [ID!]) {
  boards(ids: $boardIds) {
    id
    name
    activities(limit: 20) {
      id
      type
      created_at
      user { id name email photo_original }
      pulse { id name }
    }
  }
}

query GetUsers {
  users(limit: 50) {
    id name email photo_original title
  }
}
```

### 5. Widget Features & Constraints

**Dashboard Widget Limitations:**
- Maximum 30 widgets per dashboard
- 20K total items limit across connected boards
- Instance-level data storage
- Responsive design for various widget sizes

**User Pulse Features:**
- ✅ Real-time presence indicators
- ✅ Activity feed with recent team actions  
- ✅ Configurable settings (refresh interval, activity limit)
- ✅ Direct navigation to Monday items
- ✅ Monday.com design system compliance

### 6. Development & Testing

**Local Development:**
```bash
# Install Monday CLI
npm install -g monday-cli

# Create tunnel for local testing
monday-cli quickstart react
```

**Testing Environment:**
- Use tunnel URL in Monday's Feature Editor
- Test with real Monday.com boards and data
- Verify permissions and OAuth flow
- Test responsive design across widget sizes

### 7. Deployment Requirements

**For Monday.com Marketplace:**
- Host widget on reliable CDN or server
- Implement proper error handling
- Add loading states and empty states
- Ensure HTTPS and security compliance
- Follow Monday.com design guidelines

**App Submission Checklist:**
- [ ] App registered in Monday Developer Portal
- [ ] OAuth integration configured
- [ ] Widget tested with real Monday data
- [ ] Privacy policy and terms of service
- [ ] Support documentation
- [ ] Marketplace review submission

### 8. Compliance & Security

**Monday.com Requirements:**
- OAuth 2.0 authentication only
- Respect user permissions and scopes
- Follow Monday.com design system (Vibe)
- Implement proper error handling
- Secure data transmission (HTTPS)

**Privacy Considerations:**
- Only access data within granted permissions
- No external data storage without consent
- Respect Monday.com's data policies
- Implement proper user consent flows

### 9. Current Implementation Status

**✅ Completed:**
- Monday SDK integration
- Dual-mode app architecture
- GraphQL API implementation
- Widget context management
- Monday.com design compliance

**🔄 Next Steps for Marketplace:**
1. Register app in Monday Developer Portal
2. Configure production hosting
3. Implement OAuth flow
4. Add comprehensive error handling
5. Submit for marketplace review

### 10. File Structure

```
client/src/
├── components/
│   ├── monday-widget.tsx     # Monday SDK version
│   ├── user-pulse-widget.tsx # Standalone version
│   └── ui/                   # Shared UI components
├── pages/
│   └── user-pulse.tsx        # Router with mode detection
└── lib/
    ├── monday-api.ts         # Standalone API
    └── queryClient.ts        # React Query setup

monday-app-config.json        # App configuration
MONDAY_APP_SETUP.md          # This documentation
```

This architecture allows the app to work both as a standalone demo and as a proper Monday.com dashboard widget, meeting all the platform requirements while maintaining flexibility for development and testing.