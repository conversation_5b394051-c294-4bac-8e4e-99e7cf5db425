# User Pulse - Monday.com Widget

A Monday.com marketplace widget that provides real-time visibility into team member presence and recent activities across all accessible workspaces.

## Features

- **Real-time Team Presence**: See who's online and active across your workspaces
- **Activity Feed**: Track recent actions, updates, and changes
- **Smart Filtering**: Filter activities by workspace and user
- **Monday.com Integration**: Native widget for Monday.com dashboards
- **Responsive Design**: Works seamlessly in Monday.com's widget environment

## Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Architecture

- **Frontend**: React with TypeScript, Tailwind CSS, shadcn/ui components
- **Backend**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM (configurable)
- **Monday.com**: Native widget integration with Monday SDK

## Development

The application runs in dual mode:
1. **Standalone Mode**: Full React app for development and demos
2. **Widget Mode**: Embedded Monday.com dashboard widget

## Environment Variables

For production deployment:
- `SESSION_SECRET`: Required for session management
- `DATABASE_URL`: PostgreSQL connection string (optional, uses in-memory storage by default)
- `MONDAY_API_KEY`: Monday.com API access (for widget mode)

## Deployment

The application includes deployment fixes for:
- Environment port configuration
- Graceful error handling
- Session management
- Process monitoring

See `TRANSFER_TO_GITHUB.md` for detailed setup instructions.
