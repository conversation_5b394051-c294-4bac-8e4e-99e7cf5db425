# User Pulse - Monday.com Widget

## Overview

User Pulse is a Monday.com marketplace widget that provides real-time visibility into team member presence and recent activities across all accessible workspaces. The application is built as a dual-mode solution: a standalone React app for development/demo and a proper Monday.com dashboard widget using the Monday SDK.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript
- **Routing**: Wouter for client-side routing (standalone mode)
- **State Management**: TanStack Query for server state management (standalone mode)
- **Monday.com Integration**: Monday SDK (`monday-sdk-js`) for dashboard widget mode
- **UI Components**: Radix UI primitives with shadcn/ui design system
- **Styling**: Tailwind CSS with custom Monday.com color scheme
- **Build Tool**: Vite for development and production builds

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Runtime**: Node.js with ESM modules
- **Database**: PostgreSQL with Neon serverless connection (standalone mode)
- **ORM**: Drizzle ORM for database operations (standalone mode)
- **Session Management**: Connect-pg-simple for PostgreSQL session storage (standalone mode)

### Monday.com App Integration
- **App Type**: Dashboard Widget for Monday.com marketplace
- **SDK**: `monday-sdk-js` for API access and context management
- **Authentication**: OAuth integration with Monday.com platform
- **Data Access**: Monday GraphQL API with proper scopes (boards:read, users:read)
- **Widget Context**: Access to connected board IDs, user context, and settings

### Data Storage
- **Standalone Mode**: PostgreSQL (configured for Neon serverless)
- **Monday Widget Mode**: Monday.com's native data via GraphQL API
- **Schema**: Drizzle ORM schema definitions in shared directory (standalone)
- **Tables**: Users, workspaces, boards, items, activities, comments (standalone)
- **Migrations**: Drizzle-kit for database schema management (standalone)

## Key Components

### Data Models
- **Users**: Monday.com user integration with presence tracking
- **Workspaces**: Monday.com workspace management
- **Boards**: Board-level activity tracking
- **Items**: Task/item-level operations
- **Activities**: Comprehensive activity feed with metadata
- **Comments**: Native Monday.com commenting integration

### Frontend Components
- **UserPulseWidget**: Main widget container with filtering and settings
- **PresenceSection**: Real-time user presence indicators
- **ActivityFeed**: Scrollable activity timeline
- **CommentModal**: Modal for viewing/creating comments
- **SettingsModal**: Widget configuration interface

### Backend Services
- **Storage Layer**: Abstract storage interface with in-memory implementation
- **API Routes**: RESTful endpoints for users, workspaces, activities
- **Monday.com Integration**: API wrapper for Monday.com platform

## Data Flow

1. **User Presence**: Real-time presence updates through periodic API calls
2. **Activity Tracking**: Capture user actions across boards and items
3. **Comment System**: Leverage Monday.com's native commenting functionality
4. **Workspace Filtering**: Dynamic activity filtering by workspace/user
5. **Deep Linking**: Navigate directly to Monday.com boards/items

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL connection for Neon
- **@tanstack/react-query**: Server state management
- **@radix-ui/react-***: Headless UI components
- **drizzle-orm**: Type-safe database operations
- **wouter**: Lightweight client-side routing

### Development Dependencies
- **Vite**: Development server and build tool
- **TypeScript**: Type safety across the stack
- **Tailwind CSS**: Utility-first styling
- **ESBuild**: Server-side bundling for production

## Deployment Strategy

### Development
- Vite development server for frontend hot reloading
- tsx for TypeScript execution in development
- Concurrent frontend/backend development setup

### Production
- Frontend: Vite build with static asset optimization
- Backend: ESBuild bundling for Node.js deployment
- Database: Drizzle migrations for schema deployment
- Environment: Designed for Replit deployment with widget mode detection

### Widget Integration
- Detects Monday.com widget environment
- Responsive design for widget constraints
- Transparent background for seamless integration

## Changelog

```
Changelog:
- July 06, 2025. Initial setup
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```