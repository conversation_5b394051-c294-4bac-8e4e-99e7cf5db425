export interface MondayUser {
  id: number;
  mondayUserId: string;
  name: string;
  email: string;
  avatar: string | null;
  isOnline: boolean;
  lastSeen: Date | null;
}

export interface MondayWorkspace {
  id: number;
  mondayWorkspaceId: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

export interface MondayBoard {
  id: number;
  mondayBoardId: string;
  workspaceId: number;
  name: string;
  description: string | null;
}

export interface MondayItem {
  id: number;
  mondayItemId: string;
  boardId: number;
  name: string;
  status: string | null;
  assignedUserId: number | null;
}

export interface MondayActivity {
  id: number;
  userId: number;
  workspaceId: number;
  boardId: number;
  itemId: number;
  actionType: 'update' | 'comment' | 'assign' | 'create' | 'move';
  description: string;
  metadata: any;
  createdAt: Date;
  user: {
    id: number;
    name: string;
    avatar: string | null;
  } | null;
  workspace: {
    id: number;
    name: string;
  } | null;
  board: {
    id: number;
    name: string;
  } | null;
  item: {
    id: number;
    name: string;
    status: string | null;
  } | null;
}

export interface MondayComment {
  id: number;
  mondayCommentId: string;
  itemId: number;
  userId: number;
  content: string;
  createdAt: Date;
}

export interface WidgetSettings {
  defaultWorkspaceId: number | null;
  autoRefreshInterval: number;
  showPresenceIndicators: boolean;
  enableDesktopNotifications: boolean;
}

export interface ActivityFilters {
  workspaceId?: number;
  userId?: number;
  limit?: number;
}
