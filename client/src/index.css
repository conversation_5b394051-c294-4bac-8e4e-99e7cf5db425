@import url('https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(210, 11%, 20%);
  --muted: hsl(210, 11%, 96%);
  --muted-foreground: hsl(215, 13%, 65%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(210, 11%, 20%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(210, 11%, 20%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 11%, 96%);
  --secondary-foreground: hsl(222, 84%, 5%);
  --accent: hsl(210, 11%, 96%);
  --accent-foreground: hsl(222, 84%, 5%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(207, 90%, 54%);
  --radius: 0.5rem;

  /* Monday.com specific colors */
  --monday-blue: hsl(207, 90%, 54%);
  --monday-green: hsl(142, 70%, 45%);
  --monday-purple: hsl(259, 100%, 65%);
  --monday-pink: hsl(320, 100%, 65%);
  --monday-orange: hsl(25, 100%, 55%);
  --monday-text: hsl(210, 11%, 20%);
  --monday-secondary: hsl(215, 13%, 65%);
  --monday-light: hsl(210, 11%, 96%);
  --monday-border: hsl(214, 32%, 91%);
}

.dark {
  --background: hsl(222, 84%, 5%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(217, 32%, 17%);
  --muted-foreground: hsl(215, 20%, 65%);
  --popover: hsl(222, 84%, 5%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(222, 84%, 5%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(217, 32%, 17%);
  --input: hsl(217, 32%, 17%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(217, 32%, 17%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(217, 32%, 17%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62%, 30%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(207, 90%, 54%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Figtree', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
  }
}

@layer utilities {
  .monday-blue {
    color: var(--monday-blue);
  }
  
  .monday-green {
    color: var(--monday-green);
  }
  
  .monday-purple {
    color: var(--monday-purple);
  }
  
  .monday-pink {
    color: var(--monday-pink);
  }
  
  .monday-orange {
    color: var(--monday-orange);
  }
  
  .bg-monday-blue {
    background-color: var(--monday-blue);
  }
  
  .bg-monday-green {
    background-color: var(--monday-green);
  }
  
  .bg-monday-purple {
    background-color: var(--monday-purple);
  }
  
  .bg-monday-pink {
    background-color: var(--monday-pink);
  }
  
  .bg-monday-orange {
    background-color: var(--monday-orange);
  }
  
  .text-monday-text {
    color: var(--monday-text);
  }
  
  .text-monday-secondary {
    color: var(--monday-secondary);
  }
  
  .bg-monday-light {
    background-color: var(--monday-light);
  }
  
  .border-monday-border {
    border-color: var(--monday-border);
  }
  
  .user-avatar-sj {
    background-color: var(--monday-blue);
  }
  
  .user-avatar-mc {
    background-color: var(--monday-purple);
  }
  
  .user-avatar-er {
    background-color: var(--monday-pink);
  }
  
  .user-avatar-jd {
    background-color: var(--monday-green);
  }
  
  .user-avatar-al {
    background-color: var(--monday-orange);
  }
}
