import { apiRequest } from "./queryClient";
import type { MondayUser, MondayWorkspace, MondayActivity, MondayComment, ActivityFilters } from "../types/monday";

export class MondayAPI {
  static async getOnlineUsers(): Promise<MondayUser[]> {
    const response = await apiRequest("GET", "/api/users/online");
    return response.json();
  }

  static async getWorkspaces(): Promise<MondayWorkspace[]> {
    const response = await apiRequest("GET", "/api/workspaces");
    return response.json();
  }

  static async getActivities(filters?: ActivityFilters): Promise<MondayActivity[]> {
    const params = new URLSearchParams();
    if (filters?.workspaceId) params.append("workspaceId", filters.workspaceId.toString());
    if (filters?.userId) params.append("userId", filters.userId.toString());
    if (filters?.limit) params.append("limit", filters.limit.toString());

    const url = `/api/activities${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await apiRequest("GET", url);
    return response.json();
  }

  static async createActivity(activity: {
    userId: number;
    workspaceId: number;
    boardId: number;
    itemId: number;
    actionType: string;
    description: string;
    metadata?: any;
  }): Promise<MondayActivity> {
    const response = await apiRequest("POST", "/api/activities", activity);
    return response.json();
  }

  static async getComments(itemId: number): Promise<MondayComment[]> {
    const response = await apiRequest("GET", `/api/items/${itemId}/comments`);
    return response.json();
  }

  static async createComment(itemId: number, content: string, userId: number): Promise<MondayComment> {
    const response = await apiRequest("POST", `/api/items/${itemId}/comments`, {
      content,
      userId
    });
    return response.json();
  }

  static async updateUserPresence(mondayUserId: string, isOnline: boolean): Promise<void> {
    await apiRequest("POST", `/api/users/${mondayUserId}/presence`, { isOnline });
  }

  static async syncWithMonday(): Promise<{ success: boolean; synced: number; message: string }> {
    const response = await apiRequest("GET", "/api/monday/sync");
    return response.json();
  }
}
