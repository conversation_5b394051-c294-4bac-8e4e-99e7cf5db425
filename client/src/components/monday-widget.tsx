import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RefreshCw, Settings, Users, MessageCircle } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";
import mondaySdk from "monday-sdk-js";

// Monday SDK instance
const monday = mondaySdk();

interface MondayUser {
  id: string;
  name: string;
  email: string;
  photo_original?: string;
  is_online?: boolean;
  title?: string;
}

interface MondayBoard {
  id: string;
  name: string;
  workspace_id?: string;
}

interface MondayActivity {
  id: string;
  type: string;
  data: any;
  created_at: string;
  user: MondayUser;
  board?: MondayBoard;
  pulse?: {
    id: string;
    name: string;
  };
}

interface WidgetContext {
  instanceId: number;
  boardIds: number[];
  user: MondayUser;
  theme: 'light' | 'dark';
  editMode: boolean;
}

interface WidgetSettings {
  selectedBoardIds: number[];
  showPresenceIndicators: boolean;
  activityLimit: number;
  autoRefreshInterval: number;
}

export function MondayWidget() {
  const [context, setContext] = useState<WidgetContext | null>(null);
  const [settings, setSettings] = useState<WidgetSettings>({
    selectedBoardIds: [],
    showPresenceIndicators: true,
    activityLimit: 20,
    autoRefreshInterval: 60
  });
  const [users, setUsers] = useState<MondayUser[]>([]);
  const [activities, setActivities] = useState<MondayActivity[]>([]);
  const [boards, setBoards] = useState<MondayBoard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  
  const { toast } = useToast();

  // Initialize Monday SDK and get context
  useEffect(() => {
    // Listen for context updates
    monday.listen("context", (res) => {
      console.log("Monday context:", res.data);
      setContext(res.data as WidgetContext);
      
      // Load boards data when context is available
      if (res.data.boardIds?.length > 0) {
        loadBoardsData(res.data.boardIds);
      }
    });

    // Listen for settings updates
    monday.listen("settings", (res) => {
      console.log("Monday settings:", res.data);
      if (res.data) {
        setSettings({
          selectedBoardIds: res.data.selectedBoardIds || [],
          showPresenceIndicators: res.data.showPresenceIndicators !== false,
          activityLimit: res.data.activityLimit || 20,
          autoRefreshInterval: res.data.autoRefreshInterval || 60
        });
      }
    });

    // Get initial context
    monday.get("context").then((res) => {
      console.log("Initial context:", res.data);
      setContext(res.data as WidgetContext);
      if (res.data.boardIds?.length > 0) {
        loadBoardsData(res.data.boardIds);
      }
    });

    // Get initial settings
    monday.get("settings").then((res) => {
      console.log("Initial settings:", res.data);
      if (res.data) {
        setSettings({
          selectedBoardIds: res.data.selectedBoardIds || [],
          showPresenceIndicators: res.data.showPresenceIndicators !== false,
          activityLimit: res.data.activityLimit || 20,
          autoRefreshInterval: res.data.autoRefreshInterval || 60
        });
      }
    });

    loadUsersData();
  }, []);

  // Auto-refresh based on settings
  useEffect(() => {
    if (settings.autoRefreshInterval > 0) {
      const interval = setInterval(() => {
        refreshData();
      }, settings.autoRefreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [settings.autoRefreshInterval, context]);

  const loadBoardsData = async (boardIds: number[]) => {
    try {
      const query = `
        query($boardIds: [ID!]) {
          boards(ids: $boardIds) {
            id
            name
            workspace_id
            activities(limit: ${settings.activityLimit}) {
              id
              type
              data
              created_at
              user {
                id
                name
                email
                photo_original
              }
              pulse {
                id
                name
              }
            }
          }
        }
      `;

      const response = await monday.api(query, { variables: { boardIds } });
      
      if (response.data.boards) {
        setBoards(response.data.boards);
        
        // Aggregate activities from all boards
        const allActivities = response.data.boards.flatMap((board: any) => 
          board.activities.map((activity: any) => ({
            ...activity,
            board: { id: board.id, name: board.name, workspace_id: board.workspace_id }
          }))
        );
        
        // Sort by created date
        allActivities.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        
        setActivities(allActivities.slice(0, settings.activityLimit));
      }
    } catch (error) {
      console.error("Error loading boards data:", error);
      toast({
        title: "Error",
        description: "Failed to load board data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadUsersData = async () => {
    try {
      const query = `
        query {
          users(limit: 50) {
            id
            name
            email
            photo_original
            title
          }
        }
      `;

      const response = await monday.api(query);
      
      if (response.data.users) {
        // For demo purposes, simulate online status
        const usersWithStatus = response.data.users.map((user: MondayUser, index: number) => ({
          ...user,
          is_online: index < 3 // First 3 users are "online"
        }));
        
        setUsers(usersWithStatus);
      }
    } catch (error) {
      console.error("Error loading users data:", error);
    }
  };

  const refreshData = async () => {
    if (context?.boardIds?.length > 0) {
      await loadBoardsData(context.boardIds);
    }
    await loadUsersData();
    
    toast({
      title: "Data refreshed",
      description: "Activity feed updated successfully.",
    });
  };

  const handleOpenItem = (pulseId: string, boardId: string) => {
    // Use Monday SDK to open item
    monday.execute("openItemCard", { itemId: parseInt(pulseId), kind: "columns" });
  };

  const getUserAvatarColor = (userId: string) => {
    const colors = ['bg-monday-blue', 'bg-monday-purple', 'bg-monday-pink', 'bg-monday-green', 'bg-monday-orange'];
    const index = parseInt(userId) % colors.length;
    return colors[index];
  };

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getActivityDescription = (activity: MondayActivity) => {
    switch (activity.type) {
      case 'update_column':
        return `updated ${activity.pulse?.name || 'an item'}`;
      case 'create_pulse':
        return `created ${activity.pulse?.name || 'an item'}`;
      case 'update_name':
        return `renamed ${activity.pulse?.name || 'an item'}`;
      case 'duplicate_pulse':
        return `duplicated ${activity.pulse?.name || 'an item'}`;
      case 'archive_pulse':
        return `archived ${activity.pulse?.name || 'an item'}`;
      default:
        return `updated ${activity.pulse?.name || 'an item'}`;
    }
  };

  const onlineUsers = users.filter(user => user.is_online);

  if (isLoading) {
    return (
      <Card className="w-full bg-white shadow-sm border-monday-border">
        <CardHeader className="pb-3">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-8 rounded-lg" />
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-4 py-0">
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-6 w-6 rounded-full" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full bg-white shadow-sm border-monday-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-monday-blue rounded-lg flex items-center justify-center">
              <Users className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-monday-text">User Pulse</h3>
              <p className="text-xs text-monday-secondary">Team activity & presence</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshData}
              className="h-8 w-8 p-0 text-monday-secondary hover:text-monday-blue hover:bg-monday-light"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="h-8 w-8 p-0 text-monday-secondary hover:text-monday-blue hover:bg-monday-light"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-4 py-0">
        {/* Presence Section */}
        {settings.showPresenceIndicators && (
          <div className="py-3 border-b border-monday-border mb-4">
            <div className="flex items-center space-x-1 mb-2">
              <div className="w-3 h-3 bg-monday-green rounded-full" />
              <h4 className="text-xs font-medium text-monday-text">
                Online Now ({onlineUsers.length})
              </h4>
            </div>
            
            <div className="flex items-center space-x-2 flex-wrap">
              {onlineUsers.slice(0, 5).map((user) => (
                <div key={user.id} className="relative">
                  <Avatar className="h-8 w-8">
                    {user.photo_original ? (
                      <img src={user.photo_original} alt={user.name} className="rounded-full" />
                    ) : (
                      <AvatarFallback className={`${getUserAvatarColor(user.id)} text-white text-xs font-medium`}>
                        {getUserInitials(user.name)}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-monday-green rounded-full border-2 border-white"></div>
                </div>
              ))}
              
              {onlineUsers.length > 5 && (
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-gray-300 text-white text-xs font-medium">
                      +{onlineUsers.length - 5}
                    </AvatarFallback>
                  </Avatar>
                </div>
              )}
              
              {onlineUsers.length === 0 && (
                <p className="text-xs text-monday-secondary italic">No users online</p>
              )}
            </div>
          </div>
        )}

        {/* Activity Feed */}
        <div className="pb-4">
          <div className="flex items-center space-x-1 mb-3">
            <MessageCircle className="w-3 h-3 text-monday-secondary" />
            <h4 className="text-xs font-medium text-monday-text">Recent Activity</h4>
          </div>

          {activities.length === 0 ? (
            <div className="py-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-monday-light rounded-full flex items-center justify-center">
                <MessageCircle className="w-8 h-8 text-monday-secondary" />
              </div>
              <h3 className="text-sm font-medium text-monday-text mb-2">No recent activity</h3>
              <p className="text-xs text-monday-secondary">
                No recent activity in connected boards. Check back later!
              </p>
            </div>
          ) : (
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {activities.map((activity) => (
                  <div 
                    key={activity.id}
                    className="flex items-start space-x-3 p-2 rounded-lg hover:bg-monday-light transition-colors cursor-pointer"
                    onClick={() => activity.pulse?.id && activity.board?.id && handleOpenItem(activity.pulse.id, activity.board.id)}
                  >
                    <Avatar className="h-6 w-6 flex-shrink-0">
                      {activity.user.photo_original ? (
                        <img src={activity.user.photo_original} alt={activity.user.name} className="rounded-full" />
                      ) : (
                        <AvatarFallback className={`${getUserAvatarColor(activity.user.id)} text-white text-xs font-medium`}>
                          {getUserInitials(activity.user.name)}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-monday-text">
                        <span className="font-medium">{activity.user.name}</span>
                        <span className="ml-1 text-monday-secondary">{getActivityDescription(activity)}</span>
                      </p>
                      
                      <p className="text-xs text-monday-secondary mt-1">
                        {activity.board?.name} • {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                      </p>
                      
                      <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800 mt-1">
                        {activity.type.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </CardContent>
    </Card>
  );
}