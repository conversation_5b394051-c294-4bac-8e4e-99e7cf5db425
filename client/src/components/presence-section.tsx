import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Circle } from "lucide-react";
import type { MondayUser } from "@/types/monday";

interface PresenceSectionProps {
  users: MondayUser[];
  isLoading: boolean;
}

export function PresenceSection({ users, isLoading }: PresenceSectionProps) {
  const onlineUsers = users.filter(user => user.isOnline);
  const offlineUsers = users.filter(user => !user.isOnline);

  const getUserAvatarColor = (userId: number) => {
    const colors = ['bg-monday-blue', 'bg-monday-purple', 'bg-monday-pink', 'bg-monday-green', 'bg-monday-orange'];
    return colors[userId % colors.length];
  };

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (isLoading) {
    return (
      <div className="py-3 border-b border-monday-border mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <Skeleton className="h-3 w-3 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="flex items-center space-x-2">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-8 w-8 rounded-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="py-3 border-b border-monday-border mb-4">
      <div className="flex items-center space-x-1 mb-2">
        <Circle className="w-3 h-3 text-monday-green fill-current" />
        <h4 className="text-xs font-medium text-monday-text">
          Online Now ({onlineUsers.length})
        </h4>
      </div>
      
      <div className="flex items-center space-x-2 flex-wrap">
        {onlineUsers.slice(0, 5).map((user) => (
          <div key={user.id} className="relative">
            <Avatar className="h-8 w-8">
              <AvatarFallback className={`${getUserAvatarColor(user.id)} text-white text-xs font-medium`}>
                {getUserInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-monday-green rounded-full border-2 border-white"></div>
          </div>
        ))}
        
        {onlineUsers.length > 5 && (
          <div className="relative">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-gray-300 text-white text-xs font-medium">
                +{onlineUsers.length - 5}
              </AvatarFallback>
            </Avatar>
          </div>
        )}
        
        {onlineUsers.length === 0 && (
          <p className="text-xs text-monday-secondary italic">No users online</p>
        )}
      </div>
    </div>
  );
}
