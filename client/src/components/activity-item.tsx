import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import type { MondayActivity } from "@/types/monday";

interface ActivityItemProps {
  activity: MondayActivity;
  onOpenComments: (itemId: number) => void;
  onNavigateToItem: (itemId: number, boardId: number) => void;
}

export function ActivityItem({ activity, onOpenComments, onNavigateToItem }: ActivityItemProps) {
  const getUserAvatarColor = (userId: number) => {
    const colors = ['bg-monday-blue', 'bg-monday-purple', 'bg-monday-pink', 'bg-monday-green', 'bg-monday-orange'];
    return colors[userId % colors.length];
  };

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'done':
        return 'bg-monday-green/10 text-monday-green';
      case 'in progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'to do':
        return 'bg-blue-100 text-blue-800';
      case 'new':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionText = (actionType: string, description: string) => {
    switch (actionType) {
      case 'update':
        return { verb: 'updated', color: 'text-monday-secondary' };
      case 'comment':
        return { verb: 'commented on', color: 'text-monday-secondary' };
      case 'assign':
        return { verb: 'assigned', color: 'text-monday-secondary' };
      case 'create':
        return { verb: 'created', color: 'text-monday-secondary' };
      case 'move':
        return { verb: 'moved', color: 'text-monday-secondary' };
      default:
        return { verb: 'updated', color: 'text-monday-secondary' };
    }
  };

  const actionText = getActionText(activity.actionType, activity.description);

  return (
    <div 
      className="flex items-start space-x-3 p-2 rounded-lg hover:bg-monday-light transition-colors cursor-pointer"
      onClick={() => onNavigateToItem(activity.itemId, activity.boardId)}
    >
      <Avatar className="h-6 w-6 flex-shrink-0">
        <AvatarFallback className={`${getUserAvatarColor(activity.userId)} text-white text-xs font-medium`}>
          {activity.user ? getUserInitials(activity.user.name) : 'U'}
        </AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="text-xs text-monday-text">
            <span className="font-medium">{activity.user?.name}</span>
            <span className={`ml-1 ${actionText.color}`}>{actionText.verb}</span>
            <span className="ml-1 font-medium text-monday-blue hover:underline">
              {activity.item?.name}
            </span>
          </p>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onOpenComments(activity.itemId);
            }}
            className="h-6 w-6 p-0 text-monday-secondary hover:text-monday-blue"
          >
            <MessageCircle className="w-3 h-3" />
          </Button>
        </div>
        
        <p className="text-xs text-monday-secondary mt-1">
          {activity.board?.name} • {formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
        </p>
        
        <div className="flex items-center space-x-2 mt-1">
          {activity.item?.status && (
            <Badge variant="secondary" className={`text-xs ${getStatusBadgeColor(activity.item.status)}`}>
              {activity.actionType === 'update' ? `Status: ${activity.item.status}` : activity.item.status}
            </Badge>
          )}
          
          {activity.actionType === 'create' && (
            <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
              New Item
            </Badge>
          )}
          
          {activity.actionType === 'assign' && activity.metadata?.assignedTo && (
            <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
              Assigned to {activity.metadata.assignedTo}
            </Badge>
          )}
        </div>
        
        {activity.actionType === 'comment' && activity.metadata?.comment && (
          <div className="bg-gray-50 rounded-md p-2 mt-1">
            <p className="text-xs text-monday-text">"{activity.metadata.comment}"</p>
          </div>
        )}
      </div>
    </div>
  );
}
