import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RefreshCw, Settings, Users } from "lucide-react";
import { PresenceSection } from "./presence-section";
import { ActivityFeed } from "./activity-feed";
import { CommentModal } from "./comment-modal";
import { SettingsModal } from "./settings-modal";
import { useWorkspaces, useActivities, useOnlineUsers, useSyncWithMonday } from "@/hooks/use-monday-data";
import { useToast } from "@/hooks/use-toast";
import type { WidgetSettings } from "@/types/monday";

export function UserPulseWidget() {
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState<number | undefined>();
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>();
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
  const [settings, setSettings] = useState<WidgetSettings>({
    defaultWorkspaceId: null,
    autoRefreshInterval: 60,
    showPresenceIndicators: true,
    enableDesktopNotifications: false,
  });

  const { toast } = useToast();
  const { data: workspaces, isLoading: workspacesLoading } = useWorkspaces();
  const { data: onlineUsers, isLoading: usersLoading } = useOnlineUsers();
  const { data: activities, isLoading: activitiesLoading, refetch: refetchActivities } = useActivities({
    workspaceId: selectedWorkspaceId,
    userId: selectedUserId,
    limit: 20,
  });
  const syncMutation = useSyncWithMonday();

  // Auto-refresh based on settings
  useEffect(() => {
    if (settings.autoRefreshInterval > 0) {
      const interval = setInterval(() => {
        refetchActivities();
      }, settings.autoRefreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [settings.autoRefreshInterval, refetchActivities]);

  // Set default workspace from settings
  useEffect(() => {
    if (settings.defaultWorkspaceId && !selectedWorkspaceId) {
      setSelectedWorkspaceId(settings.defaultWorkspaceId);
    }
  }, [settings.defaultWorkspaceId, selectedWorkspaceId]);

  const handleRefresh = async () => {
    try {
      await syncMutation.mutateAsync();
      refetchActivities();
    } catch (error) {
      toast({
        title: "Refresh failed",
        description: "Unable to refresh data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleOpenComments = (itemId: number) => {
    setSelectedItemId(itemId);
    setShowCommentModal(true);
  };

  const handleNavigateToItem = (itemId: number, boardId: number) => {
    // In a real Monday.com widget, this would navigate to the item
    // For demo purposes, we'll show a toast
    toast({
      title: "Navigate to item",
      description: `Would navigate to item ${itemId} in board ${boardId}`,
    });
  };

  const isLoading = workspacesLoading || usersLoading || activitiesLoading;

  return (
    <>
      <Card className="w-full max-w-md bg-white shadow-sm border-monday-border">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-monday-blue rounded-lg flex items-center justify-center">
                <Users className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-monday-text">User Pulse</h3>
                <p className="text-xs text-monday-secondary">Team activity & presence</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={syncMutation.isPending}
                className="h-8 w-8 p-0 text-monday-secondary hover:text-monday-blue hover:bg-monday-light"
              >
                <RefreshCw className={`w-4 h-4 ${syncMutation.isPending ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettingsModal(true)}
                className="h-8 w-8 p-0 text-monday-secondary hover:text-monday-blue hover:bg-monday-light"
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-4 py-0">
          {/* Filter Controls */}
          <div className="py-3 bg-monday-light -mx-4 px-4 mb-4">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <label className="text-xs font-medium text-monday-text min-w-0 flex-shrink-0">
                  Workspace:
                </label>
                <Select
                  value={selectedWorkspaceId?.toString() || "all"}
                  onValueChange={(value) => setSelectedWorkspaceId(value === "all" ? undefined : parseInt(value))}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="All Workspaces" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Workspaces</SelectItem>
                    {workspaces?.map((workspace) => (
                      <SelectItem key={workspace.id} value={workspace.id.toString()}>
                        {workspace.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-xs font-medium text-monday-text min-w-0 flex-shrink-0">
                  Users:
                </label>
                <Select
                  value={selectedUserId?.toString() || "all"}
                  onValueChange={(value) => setSelectedUserId(value === "all" ? undefined : parseInt(value))}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="All Users" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    {onlineUsers?.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Presence Section */}
          {settings.showPresenceIndicators && (
            <PresenceSection 
              users={onlineUsers || []} 
              isLoading={usersLoading}
            />
          )}

          {/* Activity Feed */}
          <ActivityFeed
            activities={activities || []}
            isLoading={isLoading}
            onOpenComments={handleOpenComments}
            onNavigateToItem={handleNavigateToItem}
          />
        </CardContent>
      </Card>

      {/* Comment Modal */}
      <CommentModal
        isOpen={showCommentModal}
        onClose={() => setShowCommentModal(false)}
        itemId={selectedItemId}
      />

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        settings={settings}
        onSettingsChange={setSettings}
        workspaces={workspaces || []}
      />
    </>
  );
}
