import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { ActivityItem } from "./activity-item";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock } from "lucide-react";
import type { MondayActivity } from "@/types/monday";

interface ActivityFeedProps {
  activities: MondayActivity[];
  isLoading: boolean;
  onOpenComments: (itemId: number) => void;
  onNavigateToItem: (itemId: number, boardId: number) => void;
}

export function ActivityFeed({ activities, isLoading, onOpenComments, onNavigateToItem }: ActivityFeedProps) {
  if (isLoading) {
    return (
      <div className="pb-4">
        <div className="flex items-center space-x-1 mb-3">
          <Skeleton className="h-3 w-3" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-start space-x-3">
              <Skeleton className="h-6 w-6 rounded-full" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-3 w-32" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="pb-4">
      <div className="flex items-center space-x-1 mb-3">
        <Clock className="w-3 h-3 text-monday-secondary" />
        <h4 className="text-xs font-medium text-monday-text">Recent Activity</h4>
      </div>

      {activities.length === 0 ? (
        <div className="py-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-monday-light rounded-full flex items-center justify-center">
            <Clock className="w-8 h-8 text-monday-secondary" />
          </div>
          <h3 className="text-sm font-medium text-monday-text mb-2">No recent activity</h3>
          <p className="text-xs text-monday-secondary">
            No recent activity in this workspace. Encourage your team to get started!
          </p>
        </div>
      ) : (
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {activities.map((activity) => (
              <ActivityItem
                key={activity.id}
                activity={activity}
                onOpenComments={onOpenComments}
                onNavigateToItem={onNavigateToItem}
              />
            ))}
          </div>
        </ScrollArea>
      )}
    </div>
  );
}
