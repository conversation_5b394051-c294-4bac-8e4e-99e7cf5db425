import { useState } from "react";
import { Di<PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import type { WidgetSettings, MondayWorkspace } from "@/types/monday";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  settings: WidgetSettings;
  onSettingsChange: (settings: WidgetSettings) => void;
  workspaces: MondayWorkspace[];
}

export function SettingsModal({ 
  isOpen, 
  onClose, 
  settings, 
  onSettingsChange, 
  workspaces 
}: SettingsModalProps) {
  const [tempSettings, setTempSettings] = useState<WidgetSettings>(settings);
  const { toast } = useToast();

  const handleSave = () => {
    onSettingsChange(tempSettings);
    toast({
      title: "Settings saved",
      description: "Your widget settings have been updated successfully.",
    });
    onClose();
  };

  const handleCancel = () => {
    setTempSettings(settings);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-sm font-medium text-monday-text">Widget Settings</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Default Workspace */}
          <div className="space-y-2">
            <Label htmlFor="default-workspace" className="text-xs font-medium text-monday-text">
              Default Workspace
            </Label>
            <Select
              value={tempSettings.defaultWorkspaceId?.toString() || "all"}
              onValueChange={(value) => 
                setTempSettings({ 
                  ...tempSettings, 
                  defaultWorkspaceId: value === "all" ? null : parseInt(value) 
                })
              }
            >
              <SelectTrigger className="text-xs">
                <SelectValue placeholder="All Workspaces" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Workspaces</SelectItem>
                {workspaces.map((workspace) => (
                  <SelectItem key={workspace.id} value={workspace.id.toString()}>
                    {workspace.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Auto-refresh Interval */}
          <div className="space-y-2">
            <Label htmlFor="auto-refresh" className="text-xs font-medium text-monday-text">
              Auto-refresh Interval
            </Label>
            <Select
              value={tempSettings.autoRefreshInterval.toString()}
              onValueChange={(value) => 
                setTempSettings({ 
                  ...tempSettings, 
                  autoRefreshInterval: parseInt(value) 
                })
              }
            >
              <SelectTrigger className="text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30">30 seconds</SelectItem>
                <SelectItem value="60">1 minute</SelectItem>
                <SelectItem value="120">2 minutes</SelectItem>
                <SelectItem value="300">5 minutes</SelectItem>
                <SelectItem value="0">Disabled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Show Presence Indicators */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-presence"
              checked={tempSettings.showPresenceIndicators}
              onCheckedChange={(checked) => 
                setTempSettings({ 
                  ...tempSettings, 
                  showPresenceIndicators: checked === true 
                })
              }
            />
            <Label htmlFor="show-presence" className="text-xs text-monday-text">
              Show presence indicators
            </Label>
          </div>

          {/* Desktop Notifications */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="desktop-notifications"
              checked={tempSettings.enableDesktopNotifications}
              onCheckedChange={(checked) => 
                setTempSettings({ 
                  ...tempSettings, 
                  enableDesktopNotifications: checked === true 
                })
              }
            />
            <Label htmlFor="desktop-notifications" className="text-xs text-monday-text">
              Enable desktop notifications
            </Label>
          </div>
        </div>

        <div className="flex items-center justify-end space-x-2 pt-4">
          <Button variant="outline" size="sm" onClick={handleCancel}>
            Cancel
          </Button>
          <Button size="sm" onClick={handleSave} className="bg-monday-blue hover:bg-monday-blue/90">
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
