import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Paperclip, Smile, Send } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useComments, useCreateComment } from "@/hooks/use-monday-data";

interface CommentModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: number | null;
}

export function CommentModal({ isOpen, onClose, itemId }: CommentModalProps) {
  const [newComment, setNewComment] = useState("");
  const { data: comments, isLoading } = useComments(itemId || 0);
  const createCommentMutation = useCreateComment();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !itemId) return;

    try {
      await createCommentMutation.mutateAsync({
        itemId,
        content: newComment,
        userId: 1, // In a real app, this would be the current user's ID
      });
      setNewComment("");
    } catch (error) {
      console.error("Failed to create comment:", error);
    }
  };

  const getUserAvatarColor = (userId: number) => {
    const colors = ['bg-monday-blue', 'bg-monday-purple', 'bg-monday-pink', 'bg-monday-green', 'bg-monday-orange'];
    return colors[userId % colors.length];
  };

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Mock user data for demo
  const mockUsers = [
    { id: 1, name: "Sarah Johnson" },
    { id: 2, name: "Mike Chen" },
    { id: 3, name: "Emma Rodriguez" },
  ];

  const getUser = (userId: number) => {
    return mockUsers.find(u => u.id === userId) || { id: userId, name: "Unknown User" };
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-sm font-medium text-monday-text">Comments</DialogTitle>
          <p className="text-xs text-monday-secondary">Item #{itemId}</p>
        </DialogHeader>

        <div className="space-y-4">
          {/* Comments List */}
          <ScrollArea className="h-60">
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-start space-x-3">
                    <Skeleton className="h-6 w-6 rounded-full" />
                    <div className="flex-1 space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-full" />
                    </div>
                  </div>
                ))}
              </div>
            ) : comments && comments.length > 0 ? (
              <div className="space-y-3">
                {comments.map((comment) => {
                  const user = getUser(comment.userId);
                  return (
                    <div key={comment.id} className="flex items-start space-x-3">
                      <Avatar className="h-6 w-6 flex-shrink-0">
                        <AvatarFallback className={`${getUserAvatarColor(comment.userId)} text-white text-xs font-medium`}>
                          {getUserInitials(user.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs font-medium text-monday-text">{user.name}</span>
                          <span className="text-xs text-monday-secondary">
                            {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                          </span>
                        </div>
                        <p className="text-xs text-monday-text mt-1">{comment.content}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-xs text-monday-secondary">No comments yet. Be the first to comment!</p>
              </div>
            )}
          </ScrollArea>

          <Separator />

          {/* Comment Form */}
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="flex items-start space-x-2">
              <Avatar className="h-6 w-6 flex-shrink-0">
                <AvatarFallback className="bg-monday-green text-white text-xs font-medium">
                  ME
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <Textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="min-h-[60px] text-xs resize-none"
                  disabled={createCommentMutation.isPending}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-monday-secondary hover:text-monday-text"
                  title="Attach file"
                >
                  <Paperclip className="w-3 h-3" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-monday-secondary hover:text-monday-text"
                  title="Add emoji"
                >
                  <Smile className="w-3 h-3" />
                </Button>
              </div>
              <Button
                type="submit"
                size="sm"
                className="bg-monday-blue hover:bg-monday-blue/90 text-white"
                disabled={!newComment.trim() || createCommentMutation.isPending}
              >
                <Send className="w-3 h-3 mr-1" />
                Comment
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
