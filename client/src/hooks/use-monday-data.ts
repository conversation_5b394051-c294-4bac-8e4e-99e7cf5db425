import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { MondayAPI } from "../lib/monday-api";
import { useToast } from "./use-toast";
import type { ActivityFilters } from "../types/monday";

export function useOnlineUsers() {
  return useQuery({
    queryKey: ["/api/users/online"],
    queryFn: () => MondayAPI.getOnlineUsers(),
    refetchInterval: 30000, // Refresh every 30 seconds
  });
}

export function useWorkspaces() {
  return useQuery({
    queryKey: ["/api/workspaces"],
    queryFn: () => MondayAPI.getWorkspaces(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useActivities(filters?: ActivityFilters) {
  const queryKey = ["/api/activities", filters];
  
  return useQuery({
    queryKey,
    queryFn: () => MondayAPI.getActivities(filters),
    refetchInterval: 60000, // Refresh every minute
  });
}

export function useComments(itemId: number) {
  return useQuery({
    queryKey: ["/api/items", itemId, "comments"],
    queryFn: () => MondayAPI.getComments(itemId),
    enabled: !!itemId,
  });
}

export function useCreateComment() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ itemId, content, userId }: { itemId: number; content: string; userId: number }) =>
      MondayAPI.createComment(itemId, content, userId),
    onSuccess: (_, { itemId }) => {
      queryClient.invalidateQueries({ queryKey: ["/api/items", itemId, "comments"] });
      queryClient.invalidateQueries({ queryKey: ["/api/activities"] });
      toast({
        title: "Comment added",
        description: "Your comment has been posted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to post comment. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useCreateActivity() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: MondayAPI.createActivity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/activities"] });
    },
  });
}

export function useUpdateUserPresence() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ mondayUserId, isOnline }: { mondayUserId: string; isOnline: boolean }) =>
      MondayAPI.updateUserPresence(mondayUserId, isOnline),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users/online"] });
    },
  });
}

export function useSyncWithMonday() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: MondayAPI.syncWithMonday,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["/api/activities"] });
      queryClient.invalidateQueries({ queryKey: ["/api/users/online"] });
      toast({
        title: "Sync successful",
        description: data.message,
      });
    },
    onError: () => {
      toast({
        title: "Sync failed",
        description: "Failed to sync with Monday.com. Please try again.",
        variant: "destructive",
      });
    },
  });
}
