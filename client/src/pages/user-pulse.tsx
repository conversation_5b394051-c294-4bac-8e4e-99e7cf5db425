import { useState, useEffect } from "react";
import { UserPulseWidget } from "@/components/user-pulse-widget";
import { MondayWidget } from "@/components/monday-widget";

export default function UserPulsePage() {
  const [isWidgetMode, setIsWidgetMode] = useState(false);
  const [isMondayApp, setIsMondayApp] = useState(false);

  useEffect(() => {
    // Check if running in widget mode (e.g., inside Monday.com)
    const isInWidget = window.parent !== window || window.location.search.includes('widget=true');
    setIsWidgetMode(isInWidget);

    // Check if running as Monday.com app (has Monday SDK available)
    const isMondayEnvironment = window.parent !== window && typeof window.monday !== 'undefined';
    setIsMondayApp(isMondayEnvironment);
  }, []);

  // If running as Monday.com dashboard widget, use the Monday SDK version
  if (isMondayApp) {
    return (
      <div className="w-full">
        <MondayWidget />
      </div>
    );
  }

  // Otherwise, use the standalone version for development/demo
  return (
    <div className={`min-h-screen ${isWidgetMode ? 'bg-transparent' : 'bg-gray-50'} flex items-center justify-center p-4`}>
      <div className="w-full max-w-md">
        <UserPulseWidget />
      </div>
    </div>
  );
}
