import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  mondayUserId: text("monday_user_id").notNull().unique(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  avatar: text("avatar"),
  isOnline: boolean("is_online").default(false),
  lastSeen: timestamp("last_seen"),
});

export const workspaces = pgTable("workspaces", {
  id: serial("id").primaryKey(),
  mondayWorkspaceId: text("monday_workspace_id").notNull().unique(),
  name: text("name").notNull(),
  description: text("description"),
  isActive: boolean("is_active").default(true),
});

export const boards = pgTable("boards", {
  id: serial("id").primaryKey(),
  mondayBoardId: text("monday_board_id").notNull().unique(),
  workspaceId: integer("workspace_id").references(() => workspaces.id),
  name: text("name").notNull(),
  description: text("description"),
});

export const items = pgTable("items", {
  id: serial("id").primaryKey(),
  mondayItemId: text("monday_item_id").notNull().unique(),
  boardId: integer("board_id").references(() => boards.id),
  name: text("name").notNull(),
  status: text("status"),
  assignedUserId: integer("assigned_user_id").references(() => users.id),
});

export const activities = pgTable("activities", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  workspaceId: integer("workspace_id").references(() => workspaces.id),
  boardId: integer("board_id").references(() => boards.id),
  itemId: integer("item_id").references(() => items.id),
  actionType: text("action_type").notNull(), // 'update', 'comment', 'assign', 'create', 'move'
  description: text("description").notNull(),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const comments = pgTable("comments", {
  id: serial("id").primaryKey(),
  mondayCommentId: text("monday_comment_id").notNull().unique(),
  itemId: integer("item_id").references(() => items.id),
  userId: integer("user_id").references(() => users.id),
  content: text("content").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).omit({ id: true });
export const insertWorkspaceSchema = createInsertSchema(workspaces).omit({ id: true });
export const insertBoardSchema = createInsertSchema(boards).omit({ id: true });
export const insertItemSchema = createInsertSchema(items).omit({ id: true });
export const insertActivitySchema = createInsertSchema(activities).omit({ id: true });
export const insertCommentSchema = createInsertSchema(comments).omit({ id: true });

export type User = typeof users.$inferSelect;
export type Workspace = typeof workspaces.$inferSelect;
export type Board = typeof boards.$inferSelect;
export type Item = typeof items.$inferSelect;
export type Activity = typeof activities.$inferSelect;
export type Comment = typeof comments.$inferSelect;

export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertWorkspace = z.infer<typeof insertWorkspaceSchema>;
export type InsertBoard = z.infer<typeof insertBoardSchema>;
export type InsertItem = z.infer<typeof insertItemSchema>;
export type InsertActivity = z.infer<typeof insertActivitySchema>;
export type InsertComment = z.infer<typeof insertCommentSchema>;
